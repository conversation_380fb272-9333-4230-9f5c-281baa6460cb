package org.example

import com.sun.tools.javac.tree.TreeInfo.args
import javafx.application.Application
import javafx.scene.Group
import javafx.stage.Stage
import com.neuronrobotics.bowlerstudio.scripting.ScriptingEngine
import eu.mihosoft.vrl.v3d.CSG
import javafx.scene.PerspectiveCamera
import javafx.scene.Scene
import javafx.scene.SceneAntialiasing
import javafx.scene.paint.Color
import eu.mihosoft.vrl.v3d.Cube;


class CadViewer: Application() {
    override fun start(primaryStage: Stage?) {
        // Criar container raiz
        val root = Group()

        // Configurar cena 3D
        val scene = Scene(root, 800.0, 600.0, true, SceneAntialiasing.BALANCED)
        scene.fill = Color.LIGHTGRAY

        // Criar câmera
        val camera = PerspectiveCamera(true)
        camera.translateZ = -500.0
        scene.camera = camera

        // Carregar modelo CAD (exemplo: cubo)
        val cube = createCube()

        // Adicionar ao JavaFX
        val cadNode = ScriptingEngine.csgToAffine(cube)
        root.children.add(cadNode)

        // Configurar janela
        primaryStage.title = "Visualizador CAD"
        primaryStage.scene = scene
        primaryStage.show()
    }
    private fun createCube(): CSG {
        return Cube(100.0, 100.0, 100.0)
            .toCSG()
            .toZMin()

    }
}
//TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or
// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.
fun main() {
    Application.launch(CadViewer::class.java);
}